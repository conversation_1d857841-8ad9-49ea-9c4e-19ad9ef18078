/**
 * EventTask 重构核心逻辑演示
 * 
 * 本文件展示了重构后的核心检测逻辑，解决了原有的问题：
 * 1. 从基于修改时间检测改为基于发布状态检测
 * 2. 使用 getLatestActiveRelease() 获取实际发布的配置
 * 3. 对比发布内容而非修改时间
 */

// 原有问题代码（已删除）：
/*
// 过滤出前1分钟修改的配置项
List<OpenItemDTO> lastMinuteItems = namespace.getItems().stream()
        .filter(item -> item.getDataChangeLastModifiedTime() != null &&
                item.getDataChangeLastModifiedTime().after(lastMinuteStart) &&
                item.getDataChangeLastModifiedTime().before(lastMinuteEnd))
        .collect(Collectors.toList());
*/

// 新的重构逻辑：

/**
 * 核心检测方法 - 检测命名空间的发布变更
 */
private NamespaceChangeInfo detectNamespaceReleaseChanges(ApolloOpenApiClient apolloOpenApiClient,
                                                        String env, OpenNamespaceDTO namespace) {
    String namespaceKey = buildNamespaceSnapshotKey(env, namespace.getNamespaceName(), namespace.getClusterName());
    
    try {
        // 步骤1: 获取最新的发布记录（关键改进）
        OpenReleaseDTO latestRelease = apolloOpenApiClient.getLatestActiveRelease(
            "bff-mall", env, apolloNotifyCluster, namespace.getNamespaceName());
        
        if (latestRelease == null) {
            log.debug("命名空间 {} 没有发布记录", namespace.getNamespaceName());
            return null;
        }
        
        // 步骤2: 从发布记录中解析配置项（新增逻辑）
        Map<String, String> currentReleaseConfig = parseReleaseConfigurations(latestRelease);
        
        // 步骤3: 获取上次保存的发布快照
        Map<String, String> previousReleaseConfig = getPreviousReleaseSnapshot(namespaceKey);
        
        // 步骤4: 对比配置变更（核心逻辑）
        List<OpenItemDTO> changedItems = new ArrayList<>();
        List<OpenItemDTO> deletedItems = new ArrayList<>();
        
        detectConfigurationChanges(apolloOpenApiClient, env, namespace,
                                 currentReleaseConfig, previousReleaseConfig,
                                 changedItems, deletedItems);
        
        // 步骤5: 保存当前发布快照
        saveCurrentReleaseSnapshot(namespaceKey, currentReleaseConfig);
        
        // 步骤6: 如果有变更，返回变更信息
        if (!changedItems.isEmpty() || !deletedItems.isEmpty()) {
            log.info("检测到命名空间 {} 发布变更: 修改/新增 {} 项, 删除 {} 项",
                    namespace.getNamespaceName(), changedItems.size(), deletedItems.size());
            return new NamespaceChangeInfo(env, namespace.getNamespaceName(),
                                         namespace.getClusterName(), changedItems, deletedItems);
        }
        
    } catch (Exception e) {
        log.error("检测命名空间 {} 发布变更时发生异常", namespace.getNamespaceName(), e);
    }
    
    return null;
}

/**
 * 解析发布记录中的配置项
 */
private Map<String, String> parseReleaseConfigurations(OpenReleaseDTO release) {
    Map<String, String> configurations = new HashMap<>();
    
    if (release != null && release.getConfigurations() != null) {
        configurations.putAll(release.getConfigurations());
    }
    
    return configurations;
}

/**
 * 检测配置变更 - 核心对比逻辑
 */
private void detectConfigurationChanges(ApolloOpenApiClient apolloOpenApiClient, String env,
                                      OpenNamespaceDTO namespace, Map<String, String> currentConfig,
                                      Map<String, String> previousConfig, List<OpenItemDTO> changedItems,
                                      List<OpenItemDTO> deletedItems) {
    
    // 检测新增和修改的配置项
    for (Map.Entry<String, String> entry : currentConfig.entrySet()) {
        String key = entry.getKey();
        String currentValue = entry.getValue();
        String previousValue = previousConfig.get(key);
        
        // 如果是新增或值发生变化
        if (previousValue == null || !currentValue.equals(previousValue)) {
            try {
                // 获取配置项的详细信息（包括comment）
                OpenItemDTO itemDetail = apolloOpenApiClient.getItem("bff-mall", env,
                                                                    apolloNotifyCluster,
                                                                    namespace.getNamespaceName(), key);
                if (itemDetail != null) {
                    changedItems.add(itemDetail);
                }
            } catch (Exception e) {
                log.warn("获取配置项 {} 详细信息失败", key, e);
                // 创建一个基本的配置项对象
                OpenItemDTO basicItem = new OpenItemDTO();
                basicItem.setKey(key);
                basicItem.setValue(currentValue);
                basicItem.setComment("获取详细信息失败");
                changedItems.add(basicItem);
            }
        }
    }
    
    // 检测删除的配置项
    for (String previousKey : previousConfig.keySet()) {
        if (!currentConfig.containsKey(previousKey)) {
            // 创建删除项的表示
            OpenItemDTO deletedItem = new OpenItemDTO();
            deletedItem.setKey(previousKey);
            deletedItem.setValue(previousConfig.get(previousKey));
            deletedItem.setComment("配置项已删除");
            deletedItems.add(deletedItem);
        }
    }
}

/**
 * 快照管理方法
 */
private Map<String, String> getPreviousReleaseSnapshot(String namespaceKey) {
    return releaseSnapshotCache.getOrDefault(namespaceKey, new HashMap<>());
}

private void saveCurrentReleaseSnapshot(String namespaceKey, Map<String, String> currentConfig) {
    releaseSnapshotCache.put(namespaceKey, new HashMap<>(currentConfig));
}

/**
 * 重构前后对比：
 * 
 * 【原有逻辑问题】：
 * 1. 基于修改时间检测：item.getDataChangeLastModifiedTime()
 * 2. 只检测配置修改，不验证是否发布
 * 3. 可能产生误报：配置修改但未发布也会通知
 * 
 * 【新逻辑优势】：
 * 1. 基于发布状态检测：apolloOpenApiClient.getLatestActiveRelease()
 * 2. 对比实际发布的配置内容
 * 3. 准确识别发布变更，避免误报
 * 4. 完整获取配置项详细信息（包括comment）
 * 
 * 【工作流程】：
 * 1. 获取最新发布记录
 * 2. 解析发布配置
 * 3. 与上次快照对比
 * 4. 识别新增/修改/删除
 * 5. 获取详细信息
 * 6. 更新快照
 * 7. 生成通知
 */

/**
 * 数据结构变化：
 * 
 * 新增：
 * - releaseSnapshotCache: 存储发布快照
 * - parseReleaseConfigurations(): 解析发布配置
 * - detectConfigurationChanges(): 检测配置变更
 * 
 * 保留：
 * - configSnapshotCache: 保持向后兼容
 * - 原有的通知逻辑和数据结构
 */

/**
 * 测试场景：
 * 
 * 1. 新增配置：当前发布中有新的配置项
 * 2. 修改配置：配置项的值发生变化
 * 3. 删除配置：配置项从发布中移除
 * 4. 无变更：发布内容与上次相同
 * 5. 无发布：命名空间没有发布记录
 * 6. 异常处理：API调用失败的情况
 */
