package cn.dotfashion.soa.bff.manager.p2.task;

import cn.dotfashion.soa.bff.manager.p1.entity.dto.apollo.NamespaceChangeInfo;
import cn.dotfashion.soa.bff.manager.rpc.GlobalSensationWebClient;
import cn.dotfashion.soa.bff.manager.util.ApolloToMarkdownUtil;
import cn.dotfashion.soa.wechat.api.v3.RobotV3Api;
import cn.dotfashion.soa.wechat.vo.v3.req.robot.RobotMarkdownMessageReq;
import cn.dotfashion.soa.wechat.vo.v3.req.robot.RobotMessageReq;
import cn.dotfashion.soa.wechat.vo.v3.resp.BaseResp;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.openapi.client.ApolloOpenApiClient;
import com.ctrip.framework.apollo.openapi.dto.OpenItemDTO;
import com.ctrip.framework.apollo.openapi.dto.OpenNamespaceDTO;
import com.ctrip.framework.apollo.openapi.dto.OpenReleaseDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Component
@JobHandler("eventTask")
public class EventTask extends IJobHandler {

    @Autowired
    private GlobalSensationWebClient globalSensationWebClient;
    @Autowired
    private RobotV3Api robotV3Api;

    // 存储上一次发布的配置快照，key为命名空间标识，value为发布的配置项Map<key, value>
    private static final Map<String, Map<String, String>> releaseSnapshotCache = new ConcurrentHashMap<>();
    @Value("${rpcConfig.event.robotId:105054}")
    private String robotId;
    @Value("${apollo.notify.cluster:default}")
    private String apolloNotifyCluster;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        try {
            modifyConfigNotify();
        } catch (Exception e) {
            log.error("eventTask error", e);
        }
        return ReturnT.SUCCESS;
    }


    private void sendWechatMarkdown(String markdown) {
        try {
            if (StrUtil.isBlank(markdown)) {
                return;
            }
            //发送消息
            RobotMessageReq robotMessageReq = new RobotMessageReq();
            robotMessageReq.setRobotid(robotId);
            robotMessageReq.setMsgtype("markdown");
            RobotMarkdownMessageReq markdownMessageReq = new RobotMarkdownMessageReq();
            markdownMessageReq.setContent(markdown);
            robotMessageReq.setMarkdown(markdownMessageReq);
            BaseResp baseResp = robotV3Api.sendRobotMsg(robotMessageReq);
            log.info("sendWechatMarkdown success: {}", baseResp);
        } catch (Exception e) {
            log.error("sendWechatMarkdown error", e);
        }

    }


    private void modifyConfigNotify() {
        try {
            List<NamespaceChangeInfo> namespaceChanges = new ArrayList<>();

            Map<String, ApolloOpenApiClient> apolloOpenApiClientMap = SpringUtil.getBeansOfType(ApolloOpenApiClient.class);
            apolloOpenApiClientMap.forEach((key, apolloOpenApiClient) -> {
                String env = "CENTRALP1";
                if (key.endsWith("us")) {
                    env = "USP1";
                } else if (key.endsWith("eur")) {
                    env = "EUR";
                }

                // 获取所有命名空间
                List<OpenNamespaceDTO> namespaces = apolloOpenApiClient.getNamespaces("bff-mall", env, apolloNotifyCluster);
                if (namespaces == null || namespaces.isEmpty()) {
                    log.info("未找到任何命名空间 - 环境: {}", env);
                    return;
                }

                // 遍历所有命名空间
                for (OpenNamespaceDTO namespace : namespaces) {
                    try {
                        // 检测该命名空间的发布变更
                        NamespaceChangeInfo changeInfo = detectNamespaceReleaseChanges(apolloOpenApiClient, env, namespace);
                        if (changeInfo != null) {
                            namespaceChanges.add(changeInfo);
                        }
                    } catch (Exception e) {
                        log.error("检测命名空间 {} 发布变更时发生异常", namespace.getNamespaceName(), e);
                    }
                }
            });

            // 如果有变更项，生成并发送Markdown报告
            if (!namespaceChanges.isEmpty()) {
                String markdown = ApolloToMarkdownUtil.convertToMarkdown(namespaceChanges);
                sendWechatMarkdown(markdown);
            }

        } catch (Exception e) {
            log.error("查询命名空间配置信息时发生异常", e);
        }
    }

    /**
     * 检测命名空间的发布变更
     *
     * @param apolloOpenApiClient Apollo OpenAPI 客户端
     * @param env 环境
     * @param namespace 命名空间
     * @return 变更信息，如果没有变更则返回null
     */
    private NamespaceChangeInfo detectNamespaceReleaseChanges(ApolloOpenApiClient apolloOpenApiClient,
                                                            String env, OpenNamespaceDTO namespace) {
        String namespaceKey = buildNamespaceSnapshotKey(env, namespace.getNamespaceName(), namespace.getClusterName());

        try {
            // 获取最新的发布记录
            OpenReleaseDTO latestRelease = apolloOpenApiClient.getLatestActiveRelease(
                "bff-mall", env, apolloNotifyCluster, namespace.getNamespaceName());

            if (latestRelease == null) {
                log.debug("命名空间 {} 没有发布记录", namespace.getNamespaceName());
                return null;
            }

            // 从发布记录中解析配置项
            Map<String, String> currentReleaseConfig = parseReleaseConfigurations(latestRelease);

            // 获取上次保存的发布快照
            Map<String, String> previousReleaseConfig = getPreviousReleaseSnapshot(namespaceKey);

            // 对比配置变更
            List<OpenItemDTO> changedItems = new ArrayList<>();
            List<OpenItemDTO> deletedItems = new ArrayList<>();

            detectConfigurationChanges(apolloOpenApiClient, env, namespace,
                                     currentReleaseConfig, previousReleaseConfig,
                                     changedItems, deletedItems);

            // 保存当前发布快照
            saveCurrentReleaseSnapshot(namespaceKey, currentReleaseConfig);

            // 如果有变更，返回变更信息
            if (!changedItems.isEmpty() || !deletedItems.isEmpty()) {
                log.info("检测到命名空间 {} 发布变更: 修改/新增 {} 项, 删除 {} 项",
                        namespace.getNamespaceName(), changedItems.size(), deletedItems.size());
                return new NamespaceChangeInfo(env, namespace.getNamespaceName(),
                                             namespace.getClusterName(), changedItems, deletedItems);
            }

        } catch (Exception e) {
            log.error("检测命名空间 {} 发布变更时发生异常", namespace.getNamespaceName(), e);
        }

        return null;
    }

    /**
     * 构建命名空间快照的key
     */
    private String buildNamespaceSnapshotKey(String env, String namespaceName, String clusterName) {
        return String.format("apollo:snapshot:%s:%s:%s", env, namespaceName, clusterName);
    }

    /**
     * 解析发布记录中的配置项
     *
     * @param release 发布记录
     * @return 配置项Map，key为配置键，value为配置值
     */
    private Map<String, String> parseReleaseConfigurations(OpenReleaseDTO release) {
        Map<String, String> configurations = new HashMap<>();

        if (release != null && release.getConfigurations() != null) {
            configurations.putAll(release.getConfigurations());
        }

        return configurations;
    }

    /**
     * 检测配置变更
     *
     * @param apolloOpenApiClient Apollo客户端
     * @param env 环境
     * @param namespace 命名空间
     * @param currentConfig 当前发布的配置
     * @param previousConfig 上次的配置
     * @param changedItems 变更的配置项（输出参数）
     * @param deletedItems 删除的配置项（输出参数）
     */
    private void detectConfigurationChanges(ApolloOpenApiClient apolloOpenApiClient, String env,
                                          OpenNamespaceDTO namespace, Map<String, String> currentConfig,
                                          Map<String, String> previousConfig, List<OpenItemDTO> changedItems,
                                          List<OpenItemDTO> deletedItems) {

        // 检测新增和修改的配置项
        for (Map.Entry<String, String> entry : currentConfig.entrySet()) {
            String key = entry.getKey();
            String currentValue = entry.getValue();
            String previousValue = previousConfig.get(key);

            // 如果是新增或值发生变化
            if (previousValue == null || !currentValue.equals(previousValue)) {
                try {
                    // 获取配置项的详细信息（包括comment）
                    OpenItemDTO itemDetail = apolloOpenApiClient.getItem("bff-mall", env,
                                                                        apolloNotifyCluster,
                                                                        namespace.getNamespaceName(), key);
                    if (itemDetail != null) {
                        changedItems.add(itemDetail);
                    }
                } catch (Exception e) {
                    log.warn("获取配置项 {} 详细信息失败", key, e);
                    // 创建一个基本的配置项对象
                    OpenItemDTO basicItem = new OpenItemDTO();
                    basicItem.setKey(key);
                    basicItem.setValue(currentValue);
                    basicItem.setComment("获取详细信息失败");
                    changedItems.add(basicItem);
                }
            }
        }

        // 检测删除的配置项
        for (String previousKey : previousConfig.keySet()) {
            if (!currentConfig.containsKey(previousKey)) {
                // 创建删除项的表示
                OpenItemDTO deletedItem = new OpenItemDTO();
                deletedItem.setKey(previousKey);
                deletedItem.setValue(previousConfig.get(previousKey));
                deletedItem.setComment("配置项已删除");
                deletedItems.add(deletedItem);
            }
        }
    }

    /**
     * 获取上次保存的发布快照
     */
    private Map<String, String> getPreviousReleaseSnapshot(String namespaceKey) {
        return releaseSnapshotCache.getOrDefault(namespaceKey, new HashMap<>());
    }

    /**
     * 保存当前发布快照
     */
    private void saveCurrentReleaseSnapshot(String namespaceKey, Map<String, String> currentConfig) {
        releaseSnapshotCache.put(namespaceKey, new HashMap<>(currentConfig));
    }

}
