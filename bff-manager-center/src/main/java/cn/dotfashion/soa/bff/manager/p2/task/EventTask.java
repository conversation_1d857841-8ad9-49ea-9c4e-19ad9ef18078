package cn.dotfashion.soa.bff.manager.p2.task;

import cn.dotfashion.soa.bff.manager.p1.entity.dto.apollo.NamespaceChangeInfo;
import cn.dotfashion.soa.bff.manager.rpc.GlobalSensationWebClient;
import cn.dotfashion.soa.bff.manager.util.ApolloToMarkdownUtil;
import cn.dotfashion.soa.wechat.api.v3.RobotV3Api;
import cn.dotfashion.soa.wechat.vo.v3.req.robot.RobotMarkdownMessageReq;
import cn.dotfashion.soa.wechat.vo.v3.req.robot.RobotMessageReq;
import cn.dotfashion.soa.wechat.vo.v3.resp.BaseResp;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.openapi.client.ApolloOpenApiClient;
import com.ctrip.framework.apollo.openapi.dto.OpenItemDTO;
import com.ctrip.framework.apollo.openapi.dto.OpenNamespaceDTO;
import com.ctrip.framework.apollo.openapi.dto.OpenReleaseDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Component
@JobHandler("eventTask")
public class EventTask extends IJobHandler {

    @Autowired
    private GlobalSensationWebClient globalSensationWebClient;
    @Autowired
    private RobotV3Api robotV3Api;

    // 本地内存存储配置快照，key为命名空间标识，value为配置项List<OpenItemDTO>
    private static final Map<String, List<OpenItemDTO>> configSnapshotCache = new ConcurrentHashMap<>();
    @Value("${rpcConfig.event.robotId:105054}")
    private String robotId;
    @Value("${apollo.notify.cluster:default}")
    private String apolloNotifyCluster;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        try {
            modifyConfigNotify();
        } catch (Exception e) {
            log.error("eventTask error", e);
        }
        return ReturnT.SUCCESS;
    }


    private void sendWechatMarkdown(String markdown) {
        try {
            if (StrUtil.isBlank(markdown)) {
                return;
            }
            //发送消息
            RobotMessageReq robotMessageReq = new RobotMessageReq();
            robotMessageReq.setRobotid(robotId);
            robotMessageReq.setMsgtype("markdown");
            RobotMarkdownMessageReq markdownMessageReq = new RobotMarkdownMessageReq();
            markdownMessageReq.setContent(markdown);
            robotMessageReq.setMarkdown(markdownMessageReq);
            BaseResp baseResp = robotV3Api.sendRobotMsg(robotMessageReq);
            log.info("sendWechatMarkdown success: {}", baseResp);
        } catch (Exception e) {
            log.error("sendWechatMarkdown error", e);
        }

    }


    private void modifyConfigNotify() {
        try {

            List<NamespaceChangeInfo> namespaceChanges = new ArrayList<>();

            // 计算时间区间
            Date now = new Date();
            Date lastMinute = DateUtil.offsetMinute(now, -1);
            
            // 前1分钟的时间区间
            Date lastMinuteStart = DateUtil.beginOfMinute(lastMinute);
            Date lastMinuteEnd = DateUtil.endOfMinute(lastMinute);

            Map<String, ApolloOpenApiClient> apolloOpenApiClientMap = SpringUtil.getBeansOfType(ApolloOpenApiClient.class);
            apolloOpenApiClientMap.forEach((key, apolloOpenApiClient) -> {
                String env = "CENTRALP1";
                if (key.endsWith("us")) {
                    env = "USP1";
                } else if (key.endsWith("eur")) {
                    env = "EUR";
                }

                // 获取最新一次的发布记录, 后面通过这个接口来判断是否发布
//                OpenReleaseDTO openReleaseDTO = apolloOpenApiClient.getLatestActiveRelease("bff-mall", env, apolloNotifyCluster, "application");
                apolloOpenApiClient.getItem()


                // 获取所有命名空间
                List<OpenNamespaceDTO> namespaces = apolloOpenApiClient.getNamespaces("bff-mall", env, apolloNotifyCluster);
                if (namespaces == null || namespaces.isEmpty()) {
                    log.info("未找到任何命名空间");
                    return;
                }

                // 遍历所有命名空间
                for (OpenNamespaceDTO namespace : namespaces) {
                    if (namespace.getItems() == null || namespace.getItems().isEmpty()) {
                        continue;
                    }

                    String namespaceKey = buildNamespaceSnapshotKey(env, namespace.getNamespaceName(), namespace.getClusterName());
                    
                    // 获取当前配置项列表
                    List<OpenItemDTO> currentItems = new ArrayList<>(namespace.getItems());
                    
                    // 获取上次保存的配置项快照
                    List<OpenItemDTO> previousItems = getPreviousSnapshot(namespaceKey);
                    
                    // 过滤出前1分钟修改的配置项
                    List<OpenItemDTO> lastMinuteItems = namespace.getItems().stream()
                            .filter(item -> item.getDataChangeLastModifiedTime() != null &&
                                    item.getDataChangeLastModifiedTime().after(lastMinuteStart) &&
                                    item.getDataChangeLastModifiedTime().before(lastMinuteEnd))
                            .collect(Collectors.toList());

                    // 检测删除的配置项：只基于key来判断，不考虑value和comment的变化
                    List<OpenItemDTO> deletedItems = new ArrayList<>();
                    if (previousItems != null && !previousItems.isEmpty()) {
                        // 创建当前配置项的key集合
                        Set<String> currentKeys = currentItems.stream()
                                .map(OpenItemDTO::getKey)
                                .collect(Collectors.toSet());
                        
                        // 找出在上次快照中存在但当前key不存在的配置项
                        deletedItems = previousItems.stream()
                                .filter(item -> !currentKeys.contains(item.getKey()))
                                .collect(Collectors.toList());
                    }
                    
                    // 保存当前配置项快照供下次对比使用
                    saveCurrentSnapshot(namespaceKey, currentItems);

                    // 如果有变更项或删除项，创建命名空间变更信息
                    if (!lastMinuteItems.isEmpty() || !deletedItems.isEmpty()) {
                        NamespaceChangeInfo changeInfo = new NamespaceChangeInfo(env, namespace.getNamespaceName(), 
                                namespace.getClusterName(), lastMinuteItems, deletedItems);
                        namespaceChanges.add(changeInfo);
                        
                        // 记录删除操作日志
                        if (!deletedItems.isEmpty()) {
                            log.info("检测到命名空间 {} 中删除的配置项: {}", namespace.getNamespaceName(), 
                                    deletedItems.stream().map(OpenItemDTO::getKey).collect(Collectors.toList()));
                        }
                    }
                }
            });

            // 如果有变更项，生成并发送Markdown报告
            if (!namespaceChanges.isEmpty()) {
                String markdown = ApolloToMarkdownUtil.convertToMarkdown(namespaceChanges);
                sendWechatMarkdown(markdown);
            }

        } catch (Exception e) {
            log.error("查询命名空间配置信息时发生异常", e);
        }
    }

    /**
     * 构建命名空间快照的Redis key
     */
    private String buildNamespaceSnapshotKey(String env, String namespaceName, String clusterName) {
        return String.format("apollo:snapshot:%s:%s:%s", env, namespaceName, clusterName);
    }

    /**
     * 获取上次保存的配置项快照
     */
    private List<OpenItemDTO> getPreviousSnapshot(String namespaceKey) {
        return configSnapshotCache.getOrDefault(namespaceKey, new ArrayList<>());
    }

    /**
     * 保存当前配置项快照
     */
    private void saveCurrentSnapshot(String namespaceKey, List<OpenItemDTO> currentItems) {
        configSnapshotCache.put(namespaceKey, new ArrayList<>(currentItems));
    }

}
