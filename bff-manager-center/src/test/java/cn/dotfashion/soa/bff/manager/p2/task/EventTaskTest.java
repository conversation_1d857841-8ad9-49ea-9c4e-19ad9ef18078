package cn.dotfashion.soa.bff.manager.p2.task;

import cn.dotfashion.soa.bff.manager.p1.entity.dto.apollo.NamespaceChangeInfo;
import com.ctrip.framework.apollo.openapi.client.ApolloOpenApiClient;
import com.ctrip.framework.apollo.openapi.dto.OpenItemDTO;
import com.ctrip.framework.apollo.openapi.dto.OpenNamespaceDTO;
import com.ctrip.framework.apollo.openapi.dto.OpenReleaseDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * EventTask 重构后的单元测试
 * 
 * <AUTHOR> Assistant
 * @date 2025/06/26
 */
@ExtendWith(MockitoExtension.class)
class EventTaskTest {

    @InjectMocks
    private EventTask eventTask;

    @Mock
    private ApolloOpenApiClient apolloOpenApiClient;

    private static final String TEST_ENV = "CENTRALP1";
    private static final String TEST_CLUSTER = "default";
    private static final String TEST_NAMESPACE = "application";

    @BeforeEach
    void setUp() {
        // 设置测试用的配置值
        ReflectionTestUtils.setField(eventTask, "apolloNotifyCluster", TEST_CLUSTER);
    }

    @Test
    void testDetectNamespaceReleaseChanges_NewConfiguration() throws Exception {
        // 准备测试数据
        OpenNamespaceDTO namespace = createTestNamespace();
        OpenReleaseDTO release = createTestRelease();
        OpenItemDTO itemDetail = createTestItem("test.key", "test.value", "测试配置");

        // Mock Apollo API 调用
        when(apolloOpenApiClient.getLatestActiveRelease(eq("bff-mall"), eq(TEST_ENV), 
                                                       eq(TEST_CLUSTER), eq(TEST_NAMESPACE)))
                .thenReturn(release);
        when(apolloOpenApiClient.getItem(eq("bff-mall"), eq(TEST_ENV), eq(TEST_CLUSTER), 
                                        eq(TEST_NAMESPACE), eq("test.key")))
                .thenReturn(itemDetail);

        // 调用被测试的方法
        NamespaceChangeInfo result = invokeDetectNamespaceReleaseChanges(namespace);

        // 验证结果
        assertNotNull(result);
        assertEquals(TEST_ENV, result.getDataCenter());
        assertEquals(TEST_NAMESPACE, result.getNamespaceName());
        assertEquals(1, result.getChangedItems().size());
        assertEquals(0, result.getDeletedItems().size());
        assertEquals("test.key", result.getChangedItems().get(0).getKey());
    }

    @Test
    void testDetectNamespaceReleaseChanges_ConfigurationDeleted() throws Exception {
        // 第一次调用：设置初始配置
        OpenNamespaceDTO namespace = createTestNamespace();
        OpenReleaseDTO initialRelease = createTestRelease();
        OpenItemDTO itemDetail = createTestItem("test.key", "test.value", "测试配置");

        when(apolloOpenApiClient.getLatestActiveRelease(eq("bff-mall"), eq(TEST_ENV), 
                                                       eq(TEST_CLUSTER), eq(TEST_NAMESPACE)))
                .thenReturn(initialRelease);
        when(apolloOpenApiClient.getItem(eq("bff-mall"), eq(TEST_ENV), eq(TEST_CLUSTER), 
                                        eq(TEST_NAMESPACE), eq("test.key")))
                .thenReturn(itemDetail);

        // 第一次调用，建立基线
        NamespaceChangeInfo firstResult = invokeDetectNamespaceReleaseChanges(namespace);
        assertNotNull(firstResult);

        // 第二次调用：配置被删除
        OpenReleaseDTO emptyRelease = new OpenReleaseDTO();
        emptyRelease.setConfigurations(new HashMap<>());

        when(apolloOpenApiClient.getLatestActiveRelease(eq("bff-mall"), eq(TEST_ENV), 
                                                       eq(TEST_CLUSTER), eq(TEST_NAMESPACE)))
                .thenReturn(emptyRelease);

        // 第二次调用，检测删除
        NamespaceChangeInfo secondResult = invokeDetectNamespaceReleaseChanges(namespace);

        // 验证删除检测
        assertNotNull(secondResult);
        assertEquals(0, secondResult.getChangedItems().size());
        assertEquals(1, secondResult.getDeletedItems().size());
        assertEquals("test.key", secondResult.getDeletedItems().get(0).getKey());
    }

    @Test
    void testDetectNamespaceReleaseChanges_NoChanges() throws Exception {
        // 准备测试数据
        OpenNamespaceDTO namespace = createTestNamespace();
        OpenReleaseDTO release = createTestRelease();
        OpenItemDTO itemDetail = createTestItem("test.key", "test.value", "测试配置");

        when(apolloOpenApiClient.getLatestActiveRelease(eq("bff-mall"), eq(TEST_ENV), 
                                                       eq(TEST_CLUSTER), eq(TEST_NAMESPACE)))
                .thenReturn(release);
        when(apolloOpenApiClient.getItem(eq("bff-mall"), eq(TEST_ENV), eq(TEST_CLUSTER), 
                                        eq(TEST_NAMESPACE), eq("test.key")))
                .thenReturn(itemDetail);

        // 第一次调用，建立基线
        NamespaceChangeInfo firstResult = invokeDetectNamespaceReleaseChanges(namespace);
        assertNotNull(firstResult);

        // 第二次调用，相同配置
        NamespaceChangeInfo secondResult = invokeDetectNamespaceReleaseChanges(namespace);

        // 验证没有变更
        assertNull(secondResult);
    }

    @Test
    void testDetectNamespaceReleaseChanges_NoRelease() throws Exception {
        // 准备测试数据
        OpenNamespaceDTO namespace = createTestNamespace();

        // Mock 返回 null，表示没有发布记录
        when(apolloOpenApiClient.getLatestActiveRelease(eq("bff-mall"), eq(TEST_ENV),
                                                       eq(TEST_CLUSTER), eq(TEST_NAMESPACE)))
                .thenReturn(null);

        // 调用被测试的方法
        NamespaceChangeInfo result = invokeDetectNamespaceReleaseChanges(namespace);

        // 验证结果
        assertNull(result);
    }

    @Test
    void testCacheManagement() throws Exception {
        // 测试缓存管理功能
        String testKey = "test:namespace:key";
        Map<String, String> testConfig = new HashMap<>();
        testConfig.put("test.key", "test.value");

        // 调用保存方法
        ReflectionTestUtils.invokeMethod(eventTask, "saveCurrentReleaseSnapshot", testKey, testConfig);

        // 验证快照被保存
        Map<String, String> savedConfig = (Map<String, String>) ReflectionTestUtils.invokeMethod(
                eventTask, "getPreviousReleaseSnapshot", testKey);
        assertNotNull(savedConfig);
        assertEquals("test.value", savedConfig.get("test.key"));

        // 测试清理过期快照（这里需要模拟时间过期，实际测试中可能需要调整过期时间）
        ReflectionTestUtils.invokeMethod(eventTask, "cleanExpiredSnapshots");

        // 验证清理逻辑被调用（正常情况下不会立即过期）
        Map<String, String> configAfterClean = (Map<String, String>) ReflectionTestUtils.invokeMethod(
                eventTask, "getPreviousReleaseSnapshot", testKey);
        assertNotNull(configAfterClean); // 应该还存在，因为没有过期
    }

    // 辅助方法
    private OpenNamespaceDTO createTestNamespace() {
        OpenNamespaceDTO namespace = new OpenNamespaceDTO();
        namespace.setNamespaceName(TEST_NAMESPACE);
        namespace.setClusterName(TEST_CLUSTER);
        return namespace;
    }

    private OpenReleaseDTO createTestRelease() {
        OpenReleaseDTO release = new OpenReleaseDTO();
        Map<String, String> configurations = new HashMap<>();
        configurations.put("test.key", "test.value");
        release.setConfigurations(configurations);
        return release;
    }

    private OpenItemDTO createTestItem(String key, String value, String comment) {
        OpenItemDTO item = new OpenItemDTO();
        item.setKey(key);
        item.setValue(value);
        item.setComment(comment);
        return item;
    }

    private NamespaceChangeInfo invokeDetectNamespaceReleaseChanges(OpenNamespaceDTO namespace) throws Exception {
        return (NamespaceChangeInfo) ReflectionTestUtils.invokeMethod(
                eventTask, "detectNamespaceReleaseChanges", 
                apolloOpenApiClient, TEST_ENV, namespace);
    }
}
