# EventTask 缓存管理策略说明

## 问题背景

您提出的问题非常重要：**每次保存之前是否应该清空 `releaseSnapshotCache` 避免以前的数据影响？**

## 分析结论

**不需要清空整个缓存**，但需要**智能的缓存管理策略**。

## 原因分析

### 1. 为什么不需要清空整个缓存？

```java
// 每个命名空间有独立的键
private String buildNamespaceSnapshotKey(String env, String namespaceName, String clusterName) {
    return String.format("apollo:snapshot:%s:%s:%s", env, namespaceName, clusterName);
}
```

- **键隔离**：不同命名空间使用不同的键，互不影响
- **独立更新**：每次只更新特定命名空间的快照
- **持续对比**：需要保留其他命名空间的历史快照用于下次对比

### 2. 但确实存在内存管理问题

- ❌ **内存泄漏**：删除的命名空间快照永久保留
- ❌ **过期数据**：长时间运行积累大量无用快照  
- ❌ **内存增长**：缓存无限增长

## 改进方案

### 1. 新增数据结构

```java
// 原有的发布快照缓存
private static final Map<String, Map<String, String>> releaseSnapshotCache = new ConcurrentHashMap<>();

// 新增：记录快照的最后更新时间
private static final Map<String, Long> snapshotTimestamps = new ConcurrentHashMap<>();

// 新增：快照过期时间（24小时）
private static final long SNAPSHOT_EXPIRE_TIME = 24 * 60 * 60 * 1000L;
```

### 2. 智能缓存清理

```java
/**
 * 清理过期的快照数据
 */
private void cleanExpiredSnapshots() {
    long currentTime = System.currentTimeMillis();
    Set<String> expiredKeys = new HashSet<>();
    
    // 找出过期的快照
    for (Map.Entry<String, Long> entry : snapshotTimestamps.entrySet()) {
        if (currentTime - entry.getValue() > SNAPSHOT_EXPIRE_TIME) {
            expiredKeys.add(entry.getKey());
        }
    }
    
    // 清理过期数据
    for (String expiredKey : expiredKeys) {
        releaseSnapshotCache.remove(expiredKey);
        snapshotTimestamps.remove(expiredKey);
        configSnapshotCache.remove(expiredKey); // 同时清理旧的配置快照
        log.debug("清理过期快照: {}", expiredKey);
    }
    
    if (!expiredKeys.isEmpty()) {
        log.info("清理了 {} 个过期快照", expiredKeys.size());
    }
}
```

### 3. 更新保存逻辑

```java
/**
 * 保存当前发布快照
 */
private void saveCurrentReleaseSnapshot(String namespaceKey, Map<String, String> currentConfig) {
    releaseSnapshotCache.put(namespaceKey, new HashMap<>(currentConfig));
    snapshotTimestamps.put(namespaceKey, System.currentTimeMillis()); // 记录更新时间
}
```

### 4. 定期清理

```java
private void modifyConfigNotify() {
    try {
        // 每次执行任务时清理过期快照
        cleanExpiredSnapshots();
        
        // ... 其他逻辑
    } catch (Exception e) {
        log.error("查询命名空间配置信息时发生异常", e);
    }
}
```

## 缓存管理策略

### 1. 时间基础清理
- **过期时间**：24小时
- **清理时机**：每次任务执行时
- **清理范围**：同时清理发布快照和配置快照

### 2. 内存保护
- **防止泄漏**：自动清理过期数据
- **控制增长**：限制缓存大小
- **性能优化**：避免无效数据占用内存

### 3. 数据一致性
- **独立更新**：每个命名空间独立管理
- **原子操作**：快照和时间戳同步更新
- **异常安全**：清理失败不影响主逻辑

## 对比方案

### ❌ 方案1：每次清空整个缓存
```java
// 不推荐的做法
private void saveCurrentReleaseSnapshot(String namespaceKey, Map<String, String> currentConfig) {
    releaseSnapshotCache.clear(); // 清空所有缓存
    releaseSnapshotCache.put(namespaceKey, new HashMap<>(currentConfig));
}
```

**问题**：
- 丢失其他命名空间的快照
- 无法进行正确的变更对比
- 每次都会误报所有配置为新增

### ❌ 方案2：不做任何清理
```java
// 原有的做法
private void saveCurrentReleaseSnapshot(String namespaceKey, Map<String, String> currentConfig) {
    releaseSnapshotCache.put(namespaceKey, new HashMap<>(currentConfig));
}
```

**问题**：
- 内存泄漏风险
- 过期数据积累
- 长期运行性能下降

### ✅ 方案3：智能缓存管理（推荐）
```java
// 改进后的做法
private void saveCurrentReleaseSnapshot(String namespaceKey, Map<String, String> currentConfig) {
    releaseSnapshotCache.put(namespaceKey, new HashMap<>(currentConfig));
    snapshotTimestamps.put(namespaceKey, System.currentTimeMillis());
}

// 配合定期清理
private void cleanExpiredSnapshots() {
    // 智能清理过期数据
}
```

**优势**：
- 保持数据一致性
- 防止内存泄漏
- 自动清理过期数据
- 性能和功能平衡

## 监控建议

### 1. 缓存大小监控
```java
log.info("当前缓存大小 - 发布快照: {}, 时间戳: {}, 配置快照: {}", 
         releaseSnapshotCache.size(), 
         snapshotTimestamps.size(), 
         configSnapshotCache.size());
```

### 2. 清理效果监控
```java
if (!expiredKeys.isEmpty()) {
    log.info("清理了 {} 个过期快照，剩余快照数量: {}", 
             expiredKeys.size(), 
             releaseSnapshotCache.size());
}
```

### 3. 内存使用监控
- 定期检查缓存大小
- 监控内存使用趋势
- 设置告警阈值

## 配置建议

### 1. 可配置的过期时间
```java
@Value("${apollo.snapshot.expire.hours:24}")
private int snapshotExpireHours;

private long getSnapshotExpireTime() {
    return snapshotExpireHours * 60 * 60 * 1000L;
}
```

### 2. 可配置的清理策略
```java
@Value("${apollo.snapshot.cleanup.enabled:true}")
private boolean cleanupEnabled;

@Value("${apollo.snapshot.max.size:1000}")
private int maxSnapshotSize;
```

## 总结

**您的担心是正确的**，但解决方案不是清空整个缓存，而是：

1. ✅ **智能缓存管理**：基于时间的过期清理
2. ✅ **独立命名空间**：保持不同命名空间的隔离
3. ✅ **定期清理**：防止内存泄漏和过期数据积累
4. ✅ **监控告警**：及时发现和处理缓存问题

这样既保证了功能的正确性，又解决了内存管理的问题。
